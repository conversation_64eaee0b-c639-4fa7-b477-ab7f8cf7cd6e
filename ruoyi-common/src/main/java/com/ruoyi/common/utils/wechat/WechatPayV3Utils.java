package com.ruoyi.common.utils.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.ruoyi.common.config.WechatMiniAppConfig;
import com.ruoyi.common.config.WechatPayConfig;
import com.ruoyi.common.constant.WechatConstants;
import com.ruoyi.common.core.domain.WechatPayParams;
import com.ruoyi.common.core.domain.WechatPayV3Result;
import com.ruoyi.common.core.domain.request.wechat.WechatPayRequest;
import com.ruoyi.common.core.domain.request.wechat.WechatPayV3Request;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.http.HttpResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 微信支付V3版本工具类
 *
 * <AUTHOR>
 */
@Component
public class WechatPayV3Utils {
    private static final Logger log = LoggerFactory.getLogger(WechatPayV3Utils.class);

    @Autowired
    private WechatMiniAppConfig miniAppConfig;

    @Autowired
    private WechatPayConfig payConfig;

    private PrivateKey merchantPrivateKey;

    /**
     * 小程序下单
     *
     * @param payRequest 支付请求参数
     * @return 支付结果
     */
    public WechatPayV3Result jsapiPay(WechatPayRequest payRequest) {
        try {
            // 构建V3请求参数
            WechatPayV3Request v3Request = buildJsapiPayRequest(payRequest);

            // 转换为JSON
            // 配置序列化策略：驼峰转下划线
            SerializeConfig config = new SerializeConfig();
            config.setPropertyNamingStrategy(PropertyNamingStrategy.SnakeCase);
            String requestBody = JSON.toJSONString(v3Request, config);
            log.debug("微信支付V3下单请求: {}", requestBody);

            // 构建HTTP请求头
            String url = WechatConstants.WECHAT_PAY_V3_BASE_URL + WechatConstants.WECHAT_PAY_V3_JSAPI_URL;
            Map<String, String> headers = buildHttpHeaders("POST", url, requestBody);

            // 发送请求
            HttpResponse response = HttpUtils.sendSSLPostWithResponse(url, requestBody,
                WechatConstants.WECHAT_PAY_V3_CONTENT_TYPE, headers);

            log.debug("微信支付V3下单响应状态: {}", response.getStatusCode());
            log.debug("微信支付V3下单响应: {}", response.getBody());

            if (response.isSuccess()) {
                WechatPayV3Result result = JSON.parseObject(response.getBody(), WechatPayV3Result.class);
                log.info("微信支付V3下单成功，订单号: {}, prepay_id: {}", payRequest.getOutTradeNo(), result.getPrepayId());
                return result;
            } else {
                log.error("微信支付V3下单失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                return createErrorResult("微信支付下单失败：" + response.getBody());
            }
        } catch (Exception e) {
            log.error("微信支付V3下单异常", e);
            return createErrorResult("系统异常：" + e.getMessage());
        }
    }

    /**
     * 生成小程序支付参数
     *
     * @param prepayId 预支付交易会话标识
     * @return 小程序支付参数
     */
    public WechatPayParams generateMiniAppPayParams(String prepayId) {
        try {
            String appId = miniAppConfig.getAppId();
            String timeStamp = String.valueOf(Instant.now().getEpochSecond());
            String nonceStr = generateNonceStr();
            String packageValue = "prepay_id=" + prepayId;
            String signType = "RSA";

            // 构建签名字符串
            String signString = appId + "\n" + timeStamp + "\n" + nonceStr + "\n" + packageValue + "\n";
            
            // 生成签名
            String paySign = signWithRSA(signString);

            WechatPayParams payParams = new WechatPayParams(appId, timeStamp, nonceStr, packageValue, signType, paySign);
            log.info("生成小程序支付参数成功，prepay_id: {}", prepayId);
            
            return payParams;
        } catch (Exception e) {
            log.error("生成小程序支付参数异常", e);
            return null;
        }
    }

    /**
     * 查询订单
     *
     * @param outTradeNo 商户订单号
     * @return 支付结果
     */
    public WechatPayV3Result queryOrder(String outTradeNo) {
        try {
            String url = WechatConstants.WECHAT_PAY_V3_BASE_URL +
                String.format(WechatConstants.WECHAT_PAY_V3_QUERY_BY_OUT_TRADE_NO_URL, outTradeNo);
            String queryParam = "mchid=" + payConfig.getMchId();

            // 构建HTTP请求头
            Map<String, String> headers = buildHttpHeaders("GET", url + "?" + queryParam, "");

            // 发送请求
            HttpResponse response = HttpUtils.sendSSLGetWithResponse(url, queryParam, headers);

            log.debug("微信支付V3查询订单响应状态: {}", response.getStatusCode());
            log.debug("微信支付V3查询订单响应: {}", response.getBody());

            if (response.isSuccess()) {
                WechatPayV3Result result = JSON.parseObject(response.getBody(), WechatPayV3Result.class);
                log.info("微信支付V3查询订单成功，订单号: {}, 交易状态: {}", outTradeNo, result.getTradeState());
                return result;
            } else {
                log.error("微信支付V3查询订单失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                return createErrorResult("查询订单失败：" + response.getBody());
            }
        } catch (Exception e) {
            log.error("微信支付V3查询订单异常", e);
            return createErrorResult("系统异常：" + e.getMessage());
        }
    }

    /**
     * 构建小程序支付请求参数
     */
    private WechatPayV3Request buildJsapiPayRequest(WechatPayRequest payRequest) {
        WechatPayV3Request v3Request = new WechatPayV3Request();
        v3Request.setAppid(miniAppConfig.getAppId());
        v3Request.setMchid(payConfig.getMchId());
        v3Request.setDescription(payRequest.getBody());
        v3Request.setOutTradeNo(payRequest.getOutTradeNo());
        v3Request.setNotifyUrl(payConfig.getNotifyUrl());
        v3Request.setAttach(payRequest.getAttach());

        // 设置金额信息
        WechatPayV3Request.Amount amount = new WechatPayV3Request.Amount();
        amount.setTotal(payRequest.getTotalFee());
        amount.setCurrency("CNY");
        v3Request.setAmount(amount);

        // 设置支付者信息
        WechatPayV3Request.Payer payer = new WechatPayV3Request.Payer();
        payer.setOpenid(payRequest.getOpenid());
        v3Request.setPayer(payer);

        // 设置场景信息
        WechatPayV3Request.SceneInfo sceneInfo = new WechatPayV3Request.SceneInfo();
        sceneInfo.setPayerClientIp(StringUtils.isNotEmpty(payRequest.getSpbillCreateIp()) ? 
            payRequest.getSpbillCreateIp() : "127.0.0.1");
        v3Request.setSceneInfo(sceneInfo);

        // 设置过期时间
        if (StringUtils.isNotEmpty(payRequest.getTimeExpire())) {
            v3Request.setTimeExpire(payRequest.getTimeExpire());
        } else {
            // 默认30分钟后过期
            String expireTime = Instant.now().plusSeconds(payConfig.getTimeoutMinutes() * 60)
                .atZone(ZoneId.of("Asia/Shanghai"))
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'+08:00'"));
            v3Request.setTimeExpire(expireTime);
        }

        return v3Request;
    }

    /**
     * 构建HTTP请求头
     */
    private Map<String, String> buildHttpHeaders(String method, String url, String body) throws Exception {
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        String nonceStr = generateNonceStr();

        // 构建签名字符串
        String path = url.substring(WechatConstants.WECHAT_PAY_V3_BASE_URL.length());
        String signString = method + "\n" + path + "\n" + timestamp + "\n" + nonceStr + "\n" + body + "\n";

        // 生成签名
        String signature = signWithRSA(signString);

        // 构建Authorization头
        String authorization = String.format("WECHATPAY2-SHA256-RSA2048 mchid=\"%s\",nonce_str=\"%s\",timestamp=\"%s\",serial_no=\"%s\",signature=\"%s\"",
            payConfig.getMchId(), nonceStr, timestamp, payConfig.getMerchantSerialNumber(), signature);

        // 构建请求头Map
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", WechatConstants.WECHAT_PAY_V3_ACCEPT);
        headers.put("User-Agent", WechatConstants.WECHAT_PAY_V3_USER_AGENT);
        headers.put("Authorization", authorization);

        return headers;
    }

    /**
     * RSA签名
     */
    private String signWithRSA(String data) throws Exception {
        if (merchantPrivateKey == null) {
            merchantPrivateKey = loadPrivateKey();
        }

        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(merchantPrivateKey);
        signature.update(data.getBytes(StandardCharsets.UTF_8));
        
        return Base64.getEncoder().encodeToString(signature.sign());
    }

    /**
     * 加载商户私钥
     */
    private PrivateKey loadPrivateKey() throws Exception {
        String privateKeyPath = payConfig.getPrivateKeyPath();
        if (StringUtils.isEmpty(privateKeyPath)) {
            throw new IllegalArgumentException("商户私钥文件路径未配置");
        }

        String privateKeyContent = new String(Files.readAllBytes(Paths.get(privateKeyPath)), StandardCharsets.UTF_8)
            .replace("-----BEGIN PRIVATE KEY-----", "")
            .replace("-----END PRIVATE KEY-----", "")
            .replaceAll("\\s", "");

        byte[] keyBytes = Base64.getDecoder().decode(privateKeyContent);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 生成随机字符串
     */
    private String generateNonceStr() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 32);
    }

    /**
     * 创建错误结果
     */
    private WechatPayV3Result createErrorResult(String errorMsg) {
        WechatPayV3Result result = new WechatPayV3Result();
        result.setTradeState("FAIL");
        result.setTradeStateDesc(errorMsg);
        return result;
    }
}
