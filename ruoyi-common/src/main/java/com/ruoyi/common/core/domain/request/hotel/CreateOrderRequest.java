package com.ruoyi.common.core.domain.request.hotel;

import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 创建订单请求
 * 
 * <AUTHOR>
 */
public class CreateOrderRequest
{
    /** 微信用户openid */
    @NotBlank(message = "用户openid不能为空")
    private String openid;

    /** 会议ID */
    @NotNull(message = "会议ID不能为空")
    private Long conferenceId;

    /** 房间ID */
    @NotNull(message = "房间ID不能为空")
    private Long roomId;

    /** 房间类型 */
    @NotBlank(message = "房间类型不能为空")
    private String roomType;

    /** 房间名称 */
    @NotBlank(message = "房间名称不能为空")
    private String roomName;

    /** 入住日期 */
    @NotNull(message = "入住日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkinDate;

    /** 退房日期 */
    @NotNull(message = "退房日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkoutDate;

    /** 住宿天数 */
    @NotNull(message = "住宿天数不能为空")
    @Min(value = 1, message = "住宿天数至少为1天")
    private Integer nights;

    /** 房间单价 */
    @NotNull(message = "房间单价不能为空")
    @DecimalMin(value = "0.01", message = "房间单价必须大于0")
    private BigDecimal roomPrice;

    /** 订单总金额 */
    @NotNull(message = "订单总金额不能为空")
    @DecimalMin(value = "0.01", message = "订单总金额必须大于0")
    private BigDecimal totalAmount;

    /** 押金金额 */
    @NotNull(message = "押金金额不能为空")
    @DecimalMin(value = "0", message = "押金金额不能为负数")
    private BigDecimal depositAmount;

    /** 入住人姓名 */
    @NotBlank(message = "入住人姓名不能为空")
    private String guestName;

    /** 入住人电话 */
    @NotBlank(message = "入住人电话不能为空")
    private String guestPhone;

    /** 入住人身份证号 */
    private String guestIdCard;

    /** 特殊要求 */
    private String specialRequirements;

    /** 备注 */
    private String remark;

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public Long getConferenceId() {
        return conferenceId;
    }

    public void setConferenceId(Long conferenceId) {
        this.conferenceId = conferenceId;
    }

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public Date getCheckinDate() {
        return checkinDate;
    }

    public void setCheckinDate(Date checkinDate) {
        this.checkinDate = checkinDate;
    }

    public Date getCheckoutDate() {
        return checkoutDate;
    }

    public void setCheckoutDate(Date checkoutDate) {
        this.checkoutDate = checkoutDate;
    }

    public Integer getNights() {
        return nights;
    }

    public void setNights(Integer nights) {
        this.nights = nights;
    }

    public BigDecimal getRoomPrice() {
        return roomPrice;
    }

    public void setRoomPrice(BigDecimal roomPrice) {
        this.roomPrice = roomPrice;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(BigDecimal depositAmount) {
        this.depositAmount = depositAmount;
    }

    public String getGuestName() {
        return guestName;
    }

    public void setGuestName(String guestName) {
        this.guestName = guestName;
    }

    public String getGuestPhone() {
        return guestPhone;
    }

    public void setGuestPhone(String guestPhone) {
        this.guestPhone = guestPhone;
    }

    public String getGuestIdCard() {
        return guestIdCard;
    }

    public void setGuestIdCard(String guestIdCard) {
        this.guestIdCard = guestIdCard;
    }

    public String getSpecialRequirements() {
        return specialRequirements;
    }

    public void setSpecialRequirements(String specialRequirements) {
        this.specialRequirements = specialRequirements;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "CreateOrderRequest{" +
                "openid='" + openid + '\'' +
                ", conferenceId=" + conferenceId +
                ", roomId=" + roomId +
                ", roomType='" + roomType + '\'' +
                ", roomName='" + roomName + '\'' +
                ", checkinDate=" + checkinDate +
                ", checkoutDate=" + checkoutDate +
                ", nights=" + nights +
                ", roomPrice=" + roomPrice +
                ", totalAmount=" + totalAmount +
                ", depositAmount=" + depositAmount +
                ", guestName='" + guestName + '\'' +
                ", guestPhone='" + guestPhone + '\'' +
                ", guestIdCard='" + guestIdCard + '\'' +
                ", specialRequirements='" + specialRequirements + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
