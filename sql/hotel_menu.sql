-- ----------------------------
-- 酒店管理模块菜单数据
-- ----------------------------

-- 一级菜单：酒店管理
INSERT INTO sys_menu VALUES('5', '酒店管理', '0', '5', '#', '', 'M', '0', '1', '', 'fa fa-hotel', 'admin', sysdate(), '', null, '酒店管理目录');

-- 二级菜单
INSERT INTO sys_menu VALUES('500', '会议管理', '5', '1', '/hotel/conference', '', 'C', '0', '1', 'hotel:conference:view', 'fa fa-calendar', 'admin', sysdate(), '', null, '会议管理菜单');
INSERT INTO sys_menu VALUES('501', '房型管理', '5', '2', '/hotel/room', '', 'C', '0', '1', 'hotel:room:view', 'fa fa-bed', 'admin', sysdate(), '', null, '房型管理菜单');
INSERT INTO sys_menu VALUES('502', '识别码管理', '5', '3', '/hotel/categoryCode', '', 'C', '0', '1', 'hotel:categoryCode:view', 'fa fa-key', 'admin', sysdate(), '', null, '识别码管理菜单');
INSERT INTO sys_menu VALUES('503', '订单管理', '5', '4', '/hotel/order', '', 'C', '0', '1', 'hotel:order:view', 'fa fa-list-alt', 'admin', sysdate(), '', null, '订单管理菜单');

-- 会议管理按钮
INSERT INTO sys_menu VALUES('5000', '会议查询', '500', '1', '#', '', 'F', '0', '1', 'hotel:conference:list', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5001', '会议新增', '500', '2', '#', '', 'F', '0', '1', 'hotel:conference:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5002', '会议修改', '500', '3', '#', '', 'F', '0', '1', 'hotel:conference:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5003', '会议删除', '500', '4', '#', '', 'F', '0', '1', 'hotel:conference:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5004', '会议导出', '500', '5', '#', '', 'F', '0', '1', 'hotel:conference:export', '#', 'admin', sysdate(), '', null, '');

-- 房型管理按钮
INSERT INTO sys_menu VALUES('5010', '房型查询', '501', '1', '#', '', 'F', '0', '1', 'hotel:room:list', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5011', '房型新增', '501', '2', '#', '', 'F', '0', '1', 'hotel:room:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5012', '房型修改', '501', '3', '#', '', 'F', '0', '1', 'hotel:room:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5013', '房型删除', '501', '4', '#', '', 'F', '0', '1', 'hotel:room:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5014', '房型导出', '501', '5', '#', '', 'F', '0', '1', 'hotel:room:export', '#', 'admin', sysdate(), '', null, '');

-- 识别码管理按钮
INSERT INTO sys_menu VALUES('5020', '识别码查询', '502', '1', '#', '', 'F', '0', '1', 'hotel:categoryCode:list', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5021', '识别码新增', '502', '2', '#', '', 'F', '0', '1', 'hotel:categoryCode:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5022', '识别码修改', '502', '3', '#', '', 'F', '0', '1', 'hotel:categoryCode:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5023', '识别码删除', '502', '4', '#', '', 'F', '0', '1', 'hotel:categoryCode:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5024', '识别码导出', '502', '5', '#', '', 'F', '0', '1', 'hotel:categoryCode:export', '#', 'admin', sysdate(), '', null, '');

-- 订单管理按钮
INSERT INTO sys_menu VALUES('5030', '订单查询', '503', '1', '#', '', 'F', '0', '1', 'hotel:order:list', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5031', '订单新增', '503', '2', '#', '', 'F', '0', '1', 'hotel:order:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5032', '订单修改', '503', '3', '#', '', 'F', '0', '1', 'hotel:order:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5033', '订单删除', '503', '4', '#', '', 'F', '0', '1', 'hotel:order:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('5034', '订单导出', '503', '5', '#', '', 'F', '0', '1', 'hotel:order:export', '#', 'admin', sysdate(), '', null, '');

-- 为超级管理员角色分配酒店管理权限
INSERT INTO sys_role_menu VALUES ('1', '5');
INSERT INTO sys_role_menu VALUES ('1', '500');
INSERT INTO sys_role_menu VALUES ('1', '501');
INSERT INTO sys_role_menu VALUES ('1', '502');
INSERT INTO sys_role_menu VALUES ('1', '5000');
INSERT INTO sys_role_menu VALUES ('1', '5001');
INSERT INTO sys_role_menu VALUES ('1', '5002');
INSERT INTO sys_role_menu VALUES ('1', '5003');
INSERT INTO sys_role_menu VALUES ('1', '5004');
INSERT INTO sys_role_menu VALUES ('1', '5010');
INSERT INTO sys_role_menu VALUES ('1', '5011');
INSERT INTO sys_role_menu VALUES ('1', '5012');
INSERT INTO sys_role_menu VALUES ('1', '5013');
INSERT INTO sys_role_menu VALUES ('1', '5014');
INSERT INTO sys_role_menu VALUES ('1', '5020');
INSERT INTO sys_role_menu VALUES ('1', '5021');
INSERT INTO sys_role_menu VALUES ('1', '5022');
INSERT INTO sys_role_menu VALUES ('1', '5023');
INSERT INTO sys_role_menu VALUES ('1', '5024');
INSERT INTO sys_role_menu VALUES ('1', '503');
INSERT INTO sys_role_menu VALUES ('1', '5030');
INSERT INTO sys_role_menu VALUES ('1', '5031');
INSERT INTO sys_role_menu VALUES ('1', '5032');
INSERT INTO sys_role_menu VALUES ('1', '5033');
INSERT INTO sys_role_menu VALUES ('1', '5034');
