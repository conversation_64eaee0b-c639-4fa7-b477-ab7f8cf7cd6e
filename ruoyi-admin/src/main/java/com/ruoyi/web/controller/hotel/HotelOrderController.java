package com.ruoyi.web.controller.hotel;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HotelOrder;
import com.ruoyi.system.domain.Conference;
import com.ruoyi.system.domain.Room;
import com.ruoyi.system.service.IHotelOrderService;
import com.ruoyi.system.service.IConferenceService;
import com.ruoyi.system.service.IRoomService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.Convert;

/**
 * 酒店订单Controller
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Controller
@RequestMapping("/hotel/order")
public class HotelOrderController extends BaseController
{
    private String prefix = "hotel/order";

    @Autowired
    private IHotelOrderService hotelOrderService;

    @Autowired
    private IConferenceService conferenceService;

    @Autowired
    private IRoomService roomService;

    @RequiresPermissions("hotel:order:view")
    @GetMapping()
    public String order()
    {
        return prefix + "/order";
    }

    /**
     * 查询酒店订单列表
     */
    @RequiresPermissions("hotel:order:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(HotelOrder hotelOrder)
    {
        startPage();
        List<HotelOrder> list = hotelOrderService.selectHotelOrderList(hotelOrder);
        return getDataTable(list);
    }

    /**
     * 导出酒店订单列表
     */
    @RequiresPermissions("hotel:order:export")
    @Log(title = "酒店订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(HotelOrder hotelOrder)
    {
        List<HotelOrder> list = hotelOrderService.selectHotelOrderList(hotelOrder);
        ExcelUtil<HotelOrder> util = new ExcelUtil<HotelOrder>(HotelOrder.class);
        return util.exportExcel(list, "酒店订单数据");
    }

    /**
     * 查看订单详情
     */
    @RequiresPermissions("hotel:order:view")
    @GetMapping("/view/{orderId}")
    public String view(@PathVariable("orderId") Long orderId, ModelMap mmap)
    {
        HotelOrder hotelOrder = hotelOrderService.selectHotelOrderByOrderId(orderId);
        mmap.put("hotelOrder", hotelOrder);
        
        // 获取关联的会议信息
        if (hotelOrder.getConferenceId() != null) {
            Conference conference = conferenceService.selectConferenceById(hotelOrder.getConferenceId());
            mmap.put("conference", conference);
        }
        
        // 获取关联的房型信息
        if (hotelOrder.getRoomId() != null) {
            Room room = roomService.selectRoomById(hotelOrder.getRoomId());
            mmap.put("room", room);
        }
        
        return prefix + "/view";
    }

    /**
     * 修改订单状态页面
     */
    @RequiresPermissions("hotel:order:edit")
    @GetMapping("/edit/{orderId}")
    public String edit(@PathVariable("orderId") Long orderId, ModelMap mmap)
    {
        HotelOrder hotelOrder = hotelOrderService.selectHotelOrderByOrderId(orderId);
        mmap.put("hotelOrder", hotelOrder);
        
        // 获取关联的会议信息
        if (hotelOrder.getConferenceId() != null) {
            Conference conference = conferenceService.selectConferenceById(hotelOrder.getConferenceId());
            mmap.put("conference", conference);
        }
        
        // 获取关联的房型信息
        if (hotelOrder.getRoomId() != null) {
            Room room = roomService.selectRoomById(hotelOrder.getRoomId());
            mmap.put("room", room);
        }
        
        return prefix + "/edit";
    }

    /**
     * 修改保存订单状态
     */
    @RequiresPermissions("hotel:order:edit")
    @Log(title = "酒店订单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(HotelOrder hotelOrder)
    {
        // 只允许修改订单状态和备注
        HotelOrder originalOrder = hotelOrderService.selectHotelOrderByOrderId(hotelOrder.getOrderId());
        if (originalOrder == null) {
            return error("订单不存在");
        }

        // 更新订单状态
        if (!originalOrder.getOrderStatus().equals(hotelOrder.getOrderStatus())) {
            int result = hotelOrderService.updateOrderStatus(originalOrder.getOrderNo(), 
                hotelOrder.getOrderStatus(), getLoginName(), "管理员手动修改");
            if (result <= 0) {
                return error("更新订单状态失败");
            }
        }

        // 更新支付状态
        if (!originalOrder.getPaymentStatus().equals(hotelOrder.getPaymentStatus())) {
            int result = hotelOrderService.updatePaymentStatus(originalOrder.getOrderNo(), 
                hotelOrder.getPaymentStatus(), getLoginName(), "管理员手动修改");
            if (result <= 0) {
                return error("更新支付状态失败");
            }
        }

        // 更新备注
        if (hotelOrder.getRemark() != null && !hotelOrder.getRemark().equals(originalOrder.getRemark())) {
            originalOrder.setRemark(hotelOrder.getRemark());
            originalOrder.setUpdateBy(getLoginName());
            hotelOrderService.updateHotelOrder(originalOrder);
        }

        return success();
    }

    /**
     * 确认订单
     */
    @RequiresPermissions("hotel:order:edit")
    @Log(title = "酒店订单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{orderId}")
    @ResponseBody
    public AjaxResult confirm(@PathVariable("orderId") Long orderId)
    {
        HotelOrder hotelOrder = hotelOrderService.selectHotelOrderByOrderId(orderId);
        if (hotelOrder == null) {
            return error("订单不存在");
        }

        int result = hotelOrderService.confirmOrder(hotelOrder.getOrderNo(), getLoginName());
        return toAjax(result);
    }

    /**
     * 取消订单
     */
    @RequiresPermissions("hotel:order:edit")
    @Log(title = "酒店订单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{orderId}")
    @ResponseBody
    public AjaxResult cancel(@PathVariable("orderId") Long orderId, String cancelReason)
    {
        HotelOrder hotelOrder = hotelOrderService.selectHotelOrderByOrderId(orderId);
        if (hotelOrder == null) {
            return error("订单不存在");
        }

        if (cancelReason == null || cancelReason.trim().isEmpty()) {
            cancelReason = "管理员取消订单";
        }

        int result = hotelOrderService.cancelOrder(hotelOrder.getOrderNo(), cancelReason, getLoginName());
        return toAjax(result);
    }

    /**
     * 删除酒店订单
     */
    @RequiresPermissions("hotel:order:remove")
    @Log(title = "酒店订单", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(hotelOrderService.deleteHotelOrderByOrderIds(Convert.toLongArray(ids)));
    }

    /**
     * 获取订单统计信息
     */
    @RequiresPermissions("hotel:order:list")
    @PostMapping("/statistics")
    @ResponseBody
    public AjaxResult statistics()
    {
        // 统计各状态订单数量
        HotelOrder queryOrder = new HotelOrder();
        
        queryOrder.setOrderStatus(HotelOrder.OrderStatus.PENDING);
        int pendingCount = hotelOrderService.countHotelOrders(queryOrder);
        
        queryOrder.setOrderStatus(HotelOrder.OrderStatus.PAID);
        int paidCount = hotelOrderService.countHotelOrders(queryOrder);
        
        queryOrder.setOrderStatus(HotelOrder.OrderStatus.CONFIRMED);
        int confirmedCount = hotelOrderService.countHotelOrders(queryOrder);
        
        queryOrder.setOrderStatus(HotelOrder.OrderStatus.CANCELLED);
        int cancelledCount = hotelOrderService.countHotelOrders(queryOrder);
        
        queryOrder.setOrderStatus(HotelOrder.OrderStatus.REFUNDED);
        int refundedCount = hotelOrderService.countHotelOrders(queryOrder);

        // 构建统计结果
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();
        statistics.put("pendingCount", pendingCount);
        statistics.put("paidCount", paidCount);
        statistics.put("confirmedCount", confirmedCount);
        statistics.put("cancelledCount", cancelledCount);
        statistics.put("refundedCount", refundedCount);
        statistics.put("totalCount", pendingCount + paidCount + confirmedCount + cancelledCount + refundedCount);

        return success(statistics);
    }
}
