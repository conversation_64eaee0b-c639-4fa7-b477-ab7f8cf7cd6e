<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>订单号：</label>
                                <input type="text" name="orderNo"/>
                            </li>
                            <li>
                                <label>入住人姓名：</label>
                                <input type="text" name="guestName"/>
                            </li>
                            <li>
                                <label>入住人电话：</label>
                                <input type="text" name="guestPhone"/>
                            </li>
                            <li>
                                <label>订单状态：</label>
                                <select name="orderStatus">
                                    <option value="">所有</option>
                                    <option value="PENDING">待支付</option>
                                    <option value="PAID">已支付</option>
                                    <option value="CONFIRMED">已确认</option>
                                    <option value="CANCELLED">已取消</option>
                                    <option value="REFUNDED">已退款</option>
                                </select>
                            </li>
                            <li>
                                <label>支付状态：</label>
                                <select name="paymentStatus">
                                    <option value="">所有</option>
                                    <option value="UNPAID">未支付</option>
                                    <option value="PAID">已支付</option>
                                    <option value="REFUNDED">已退款</option>
                                </select>
                            </li>
                            <li>
                                <label>入住日期：</label>
                                <input type="text" class="time-input" placeholder="请选择入住日期" name="checkinDate"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-info" onclick="showStatistics()" shiro:hasPermission="hotel:order:list">
                    <i class="fa fa-bar-chart"></i> 统计
                </a>
                <a class="btn btn-success single disabled" onclick="$.operate.edit()" shiro:hasPermission="hotel:order:edit">
                    <i class="fa fa-edit"></i> 修改状态
                </a>
                <a class="btn btn-primary single disabled" onclick="confirmOrder()" shiro:hasPermission="hotel:order:edit">
                    <i class="fa fa-check"></i> 确认订单
                </a>
                <a class="btn btn-danger single disabled" onclick="cancelOrder()" shiro:hasPermission="hotel:order:edit">
                    <i class="fa fa-times"></i> 取消订单
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="hotel:order:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="hotel:order:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('hotel:order:edit')}]];
        var removeFlag = [[${@permission.hasPermi('hotel:order:remove')}]];
        var prefix = ctx + "hotel/order";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "订单",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'orderId',
                    title: '订单ID',
                    visible: false
                },
                {
                    field: 'orderNo',
                    title: '订单号',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs btn-outline btn-default" href="javascript:void(0)" onclick="$.operate.view(\'' + row.orderId + '\')">' + value + '</a>');
                        return actions.join('');
                    }
                },
                {
                    field: 'guestName',
                    title: '入住人'
                },
                {
                    field: 'guestPhone',
                    title: '联系电话'
                },
                {
                    field: 'roomName',
                    title: '房间名称'
                },
                {
                    field: 'checkinDate',
                    title: '入住日期'
                },
                {
                    field: 'checkoutDate',
                    title: '退房日期'
                },
                {
                    field: 'nights',
                    title: '住宿天数'
                },
                {
                    field: 'totalAmount',
                    title: '订单金额',
                    formatter: function(value, row, index) {
                        return '¥' + parseFloat(value).toFixed(2);
                    }
                },
                {
                    field: 'orderStatus',
                    title: '订单状态',
                    formatter: function(value, row, index) {
                        var statusMap = {
                            'PENDING': '<span class="label label-warning">待支付</span>',
                            'PAID': '<span class="label label-primary">已支付</span>',
                            'CONFIRMED': '<span class="label label-success">已确认</span>',
                            'CANCELLED': '<span class="label label-danger">已取消</span>',
                            'REFUNDED': '<span class="label label-info">已退款</span>'
                        };
                        return statusMap[value] || value;
                    }
                },
                {
                    field: 'paymentStatus',
                    title: '支付状态',
                    formatter: function(value, row, index) {
                        var statusMap = {
                            'UNPAID': '<span class="label label-warning">未支付</span>',
                            'PAID': '<span class="label label-success">已支付</span>',
                            'REFUNDED': '<span class="label label-info">已退款</span>'
                        };
                        return statusMap[value] || value;
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.view(\'' + row.orderId + '\')"><i class="fa fa-search"></i>查看</a> ');
                        if (editFlag) {
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.edit(\'' + row.orderId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        }
                        if (editFlag && row.orderStatus === 'PAID') {
                            actions.push('<a class="btn btn-primary btn-xs " href="javascript:void(0)" onclick="confirmSingleOrder(\'' + row.orderId + '\')"><i class="fa fa-check"></i>确认</a> ');
                        }
                        if (editFlag && (row.orderStatus === 'PENDING' || row.orderStatus === 'PAID')) {
                            actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="cancelSingleOrder(\'' + row.orderId + '\')"><i class="fa fa-times"></i>取消</a> ');
                        }
                        if (removeFlag) {
                            actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.orderId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 显示统计信息
        function showStatistics() {
            $.post(prefix + "/statistics", function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var content = '<div class="row">' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.totalCount + '</h3><p>总订单</p></div></div>' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.pendingCount + '</h3><p>待支付</p></div></div>' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.paidCount + '</h3><p>已支付</p></div></div>' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.confirmedCount + '</h3><p>已确认</p></div></div>' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.cancelledCount + '</h3><p>已取消</p></div></div>' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.refundedCount + '</h3><p>已退款</p></div></div>' +
                        '</div>';
                    
                    $.modal.open("订单统计", content);
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }

        // 确认订单
        function confirmOrder() {
            var rows = $.table.selectColumns("orderId");
            if (rows.length == 0) {
                $.modal.alertWarning("请选择至少一条记录");
                return;
            }
            $.modal.confirm("确认要确认选中的订单吗？", function() {
                var orderId = rows[0];
                $.post(prefix + "/confirm/" + orderId, function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

        // 取消订单
        function cancelOrder() {
            var rows = $.table.selectColumns("orderId");
            if (rows.length == 0) {
                $.modal.alertWarning("请选择至少一条记录");
                return;
            }
            $.modal.prompt("请输入取消原因", function(cancelReason) {
                var orderId = rows[0];
                $.post(prefix + "/cancel/" + orderId, {cancelReason: cancelReason}, function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

        // 确认单个订单
        function confirmSingleOrder(orderId) {
            $.modal.confirm("确认要确认此订单吗？", function() {
                $.post(prefix + "/confirm/" + orderId, function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

        // 取消单个订单
        function cancelSingleOrder(orderId) {
            $.modal.prompt("请输入取消原因", function(cancelReason) {
                $.post(prefix + "/cancel/" + orderId, {cancelReason: cancelReason}, function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }
    </script>
</body>
</html>
