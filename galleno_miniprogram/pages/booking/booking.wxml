<!--booking.wxml-->
<view class="container">
  <!-- 预订信息摘要 -->
  <view class="booking-summary">
    <view class="summary-header">
      <text class="summary-title">预订信息</text>
    </view>
    
    <view class="summary-content">
      <view class="summary-row">
        <text class="summary-label">会议</text>
        <text class="summary-value">{{bookingData.conferenceTitle}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">房型</text>
        <text class="summary-value">{{bookingData.roomName}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">入住时间</text>
        <text class="summary-value">{{bookingData.checkinDate}} 至 {{bookingData.checkoutDate}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">住宿天数</text>
        <text class="summary-value">{{bookingData.nights}}晚</text>
      </view>
    </view>
  </view>

  <!-- 入住人信息 -->
  <view class="form-section">
    <view class="section-title">入住人信息</view>


    
    <view class="form-group">
      <text class="form-label">姓名 <text class="required">*</text></text>
      <input style="width: 100%; height: 60rpx; border: 2rpx solid #e5e7eb; border-radius: 10rpx; padding: 16rpx; font-size: 28rpx; color: #1f2937; background: #ffffff; box-sizing: border-box; transition: border-color 0.3s ease;"
             type="text"
             placeholder="请输入真实姓名"
             value="{{formData.name}}"
             bindinput="onInputChange"
             bindfocus="onInputFocus"
             bindblur="onInputBlur"
             data-field="name" />

      <text class="error-text" wx:if="{{errors.name}}">{{errors.name}}</text>
    </view>

    <view class="form-group">
      <text class="form-label">手机号 <text class="required">*</text></text>
      <input style="width: 100%; height: 60rpx; border: 2rpx solid #e5e7eb; border-radius: 10rpx; padding: 16rpx; font-size: 28rpx; color: #1f2937; background: #ffffff; box-sizing: border-box; transition: border-color 0.3s ease;"
             type="text"
             placeholder="请输入手机号码"
             value="{{formData.phone}}"
             maxlength="11"
             bindinput="onInputChange"
             bindfocus="onInputFocus"
             bindblur="onInputBlur"
             data-field="phone" />

      <text class="error-text" wx:if="{{errors.phone}}">{{errors.phone}}</text>
    </view>

    <view class="form-group">
      <text class="form-label">身份证号 <text class="required">*</text></text>
      <input style="width: 100%; height: 60rpx; border: 2rpx solid #e5e7eb; border-radius: 12rpx; padding: 16rpx; font-size: 28rpx; color: #1f2937; background: #ffffff; box-sizing: border-box; transition: border-color 0.3s ease;"
             type="text"
             placeholder="请输入身份证号码"
             value="{{formData.idCard}}"
             maxlength="18"
             bindinput="onInputChange"
             bindfocus="onInputFocus"
             bindblur="onInputBlur"
             data-field="idCard" />

      <text class="error-text" wx:if="{{errors.idCard}}">{{errors.idCard}}</text>
    </view>


  </view>

  <!-- 联系方式确认 -->
  <view class="form-section">
    <view class="section-title">联系方式</view>
    
    <view class="contact-info">
      <view class="contact-item">
        <text class="contact-label">联系人</text>
        <text class="contact-value">{{formData.name || '请先填写姓名'}}</text>
      </view>
      <view class="contact-item">
        <text class="contact-label">联系电话</text>
        <text class="contact-value">{{formData.phone || '请先填写手机号'}}</text>
      </view>
    </view>
  </view>

  <!-- 费用明细 -->
  <view class="cost-section">
    <view class="section-title">费用明细</view>
    
    <view class="cost-details">
      <view class="cost-row">
        <text class="cost-label">房费 ({{bookingData.roomPrice}}/晚 × {{bookingData.nights}}晚)</text>
        <text class="cost-value">¥{{bookingData.totalPrice}}</text>
      </view>
      <view class="cost-row">
        <text class="cost-label">服务费</text>
        <text class="cost-value">¥0</text>
      </view>
      <view class="cost-divider"></view>
      <view class="cost-row total">
        <text class="cost-label">总计</text>
        <text class="cost-value">¥{{bookingData.totalPrice}}</text>
      </view>
      <view class="cost-row deposit">
        <text class="cost-label">需支付定金</text>
        <text class="cost-value">¥200</text>
      </view>
    </view>
  </view>

  <!-- 预订条款 -->
  <view class="terms-section">
    <view class="terms-checkbox" bindtap="toggleTerms">
      <text class="checkbox {{agreedToTerms ? 'checked' : ''}}">{{agreedToTerms ? '✓' : ''}}</text>
      <text class="terms-text">我已阅读并同意</text>
      <text class="terms-link" bindtap="showTerms" catchtap="true">《预订条款》</text>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>

<!-- 底部支付栏 -->
<view class="payment-bar">
  <view class="payment-info">
    <text class="payment-label">需支付定金</text>
    <text class="payment-price">¥200</text>
  </view>
  <button class="payment-btn {{canPay ? '' : 'disabled'}}" 
          disabled="{{!canPay}}" 
          bindtap="proceedToPayment">
    立即支付
  </button>
</view>
