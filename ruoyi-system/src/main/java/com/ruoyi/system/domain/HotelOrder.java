package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 酒店订单对象 hotel_order
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public class HotelOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    private Long orderId;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 微信用户openid */
    @Excel(name = "微信用户openid")
    private String openid;

    /** 会议ID */
    @Excel(name = "会议ID")
    private Long conferenceId;

    /** 房间ID */
    @Excel(name = "房间ID")
    private Long roomId;

    /** 房间类型 */
    @Excel(name = "房间类型")
    private String roomType;

    /** 房间名称 */
    @Excel(name = "房间名称")
    private String roomName;

    /** 入住日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入住日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date checkinDate;

    /** 退房日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "退房日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date checkoutDate;

    /** 住宿天数 */
    @Excel(name = "住宿天数")
    private Integer nights;

    /** 房间单价 */
    @Excel(name = "房间单价")
    private BigDecimal roomPrice;

    /** 订单总金额 */
    @Excel(name = "订单总金额")
    private BigDecimal totalAmount;

    /** 押金金额 */
    @Excel(name = "押金金额")
    private BigDecimal depositAmount;

    /** 订单状态 */
    @Excel(name = "订单状态")
    private String orderStatus;

    /** 支付状态 */
    @Excel(name = "支付状态")
    private String paymentStatus;

    /** 支付方式 */
    @Excel(name = "支付方式")
    private String paymentMethod;

    /** 微信支付交易号 */
    @Excel(name = "微信支付交易号")
    private String transactionId;

    /** 微信预支付交易会话标识 */
    private String prepayId;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date confirmTime;

    /** 取消时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "取消时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;

    /** 取消原因 */
    @Excel(name = "取消原因")
    private String cancelReason;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundAmount;

    /** 退款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退款时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    /** 退款原因 */
    @Excel(name = "退款原因")
    private String refundReason;

    /** 入住人姓名 */
    @Excel(name = "入住人姓名")
    private String guestName;

    /** 入住人电话 */
    @Excel(name = "入住人电话")
    private String guestPhone;

    /** 入住人身份证号 */
    @Excel(name = "入住人身份证号")
    private String guestIdCard;

    /** 特殊要求 */
    @Excel(name = "特殊要求")
    private String specialRequirements;

    // 订单状态枚举
    public static class OrderStatus {
        public static final String PENDING = "PENDING";     // 待支付
        public static final String PAID = "PAID";           // 已支付
        public static final String CONFIRMED = "CONFIRMED"; // 已确认
        public static final String CANCELLED = "CANCELLED"; // 已取消
        public static final String REFUNDED = "REFUNDED";   // 已退款
    }

    // 支付状态枚举
    public static class PaymentStatus {
        public static final String UNPAID = "UNPAID";       // 未支付
        public static final String PAID = "PAID";           // 已支付
        public static final String REFUNDING = "REFUNDING"; // 退款中
        public static final String REFUNDED = "REFUNDED";   // 已退款
    }

    // 支付方式枚举
    public static class PaymentMethod {
        public static final String WECHAT = "WECHAT";       // 微信支付
    }

    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }
    public void setConferenceId(Long conferenceId) 
    {
        this.conferenceId = conferenceId;
    }

    public Long getConferenceId() 
    {
        return conferenceId;
    }
    public void setRoomId(Long roomId) 
    {
        this.roomId = roomId;
    }

    public Long getRoomId() 
    {
        return roomId;
    }
    public void setRoomType(String roomType) 
    {
        this.roomType = roomType;
    }

    public String getRoomType() 
    {
        return roomType;
    }
    public void setRoomName(String roomName) 
    {
        this.roomName = roomName;
    }

    public String getRoomName() 
    {
        return roomName;
    }
    public void setCheckinDate(Date checkinDate) 
    {
        this.checkinDate = checkinDate;
    }

    public Date getCheckinDate() 
    {
        return checkinDate;
    }
    public void setCheckoutDate(Date checkoutDate) 
    {
        this.checkoutDate = checkoutDate;
    }

    public Date getCheckoutDate() 
    {
        return checkoutDate;
    }
    public void setNights(Integer nights) 
    {
        this.nights = nights;
    }

    public Integer getNights() 
    {
        return nights;
    }
    public void setRoomPrice(BigDecimal roomPrice) 
    {
        this.roomPrice = roomPrice;
    }

    public BigDecimal getRoomPrice() 
    {
        return roomPrice;
    }
    public void setTotalAmount(BigDecimal totalAmount) 
    {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() 
    {
        return totalAmount;
    }
    public void setDepositAmount(BigDecimal depositAmount) 
    {
        this.depositAmount = depositAmount;
    }

    public BigDecimal getDepositAmount() 
    {
        return depositAmount;
    }
    public void setOrderStatus(String orderStatus) 
    {
        this.orderStatus = orderStatus;
    }

    public String getOrderStatus() 
    {
        return orderStatus;
    }
    public void setPaymentStatus(String paymentStatus) 
    {
        this.paymentStatus = paymentStatus;
    }

    public String getPaymentStatus() 
    {
        return paymentStatus;
    }
    public void setPaymentMethod(String paymentMethod) 
    {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentMethod() 
    {
        return paymentMethod;
    }
    public void setTransactionId(String transactionId) 
    {
        this.transactionId = transactionId;
    }

    public String getTransactionId() 
    {
        return transactionId;
    }
    public void setPrepayId(String prepayId) 
    {
        this.prepayId = prepayId;
    }

    public String getPrepayId() 
    {
        return prepayId;
    }
    public void setPaymentTime(Date paymentTime) 
    {
        this.paymentTime = paymentTime;
    }

    public Date getPaymentTime() 
    {
        return paymentTime;
    }
    public void setConfirmTime(Date confirmTime) 
    {
        this.confirmTime = confirmTime;
    }

    public Date getConfirmTime() 
    {
        return confirmTime;
    }
    public void setCancelTime(Date cancelTime) 
    {
        this.cancelTime = cancelTime;
    }

    public Date getCancelTime() 
    {
        return cancelTime;
    }
    public void setCancelReason(String cancelReason) 
    {
        this.cancelReason = cancelReason;
    }

    public String getCancelReason() 
    {
        return cancelReason;
    }
    public void setRefundAmount(BigDecimal refundAmount) 
    {
        this.refundAmount = refundAmount;
    }

    public BigDecimal getRefundAmount() 
    {
        return refundAmount;
    }
    public void setRefundTime(Date refundTime) 
    {
        this.refundTime = refundTime;
    }

    public Date getRefundTime() 
    {
        return refundTime;
    }
    public void setRefundReason(String refundReason) 
    {
        this.refundReason = refundReason;
    }

    public String getRefundReason() 
    {
        return refundReason;
    }
    public void setGuestName(String guestName) 
    {
        this.guestName = guestName;
    }

    public String getGuestName() 
    {
        return guestName;
    }
    public void setGuestPhone(String guestPhone) 
    {
        this.guestPhone = guestPhone;
    }

    public String getGuestPhone() 
    {
        return guestPhone;
    }
    public void setGuestIdCard(String guestIdCard) 
    {
        this.guestIdCard = guestIdCard;
    }

    public String getGuestIdCard() 
    {
        return guestIdCard;
    }
    public void setSpecialRequirements(String specialRequirements) 
    {
        this.specialRequirements = specialRequirements;
    }

    public String getSpecialRequirements() 
    {
        return specialRequirements;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("orderId", getOrderId())
            .append("orderNo", getOrderNo())
            .append("userId", getUserId())
            .append("openid", getOpenid())
            .append("conferenceId", getConferenceId())
            .append("roomId", getRoomId())
            .append("roomType", getRoomType())
            .append("roomName", getRoomName())
            .append("checkinDate", getCheckinDate())
            .append("checkoutDate", getCheckoutDate())
            .append("nights", getNights())
            .append("roomPrice", getRoomPrice())
            .append("totalAmount", getTotalAmount())
            .append("depositAmount", getDepositAmount())
            .append("orderStatus", getOrderStatus())
            .append("paymentStatus", getPaymentStatus())
            .append("paymentMethod", getPaymentMethod())
            .append("transactionId", getTransactionId())
            .append("prepayId", getPrepayId())
            .append("paymentTime", getPaymentTime())
            .append("confirmTime", getConfirmTime())
            .append("cancelTime", getCancelTime())
            .append("cancelReason", getCancelReason())
            .append("refundAmount", getRefundAmount())
            .append("refundTime", getRefundTime())
            .append("refundReason", getRefundReason())
            .append("guestName", getGuestName())
            .append("guestPhone", getGuestPhone())
            .append("guestIdCard", getGuestIdCard())
            .append("specialRequirements", getSpecialRequirements())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
