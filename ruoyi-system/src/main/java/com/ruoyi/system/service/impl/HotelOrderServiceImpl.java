package com.ruoyi.system.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.HotelOrderMapper;
import com.ruoyi.system.mapper.HotelOrderStatusLogMapper;
import com.ruoyi.system.domain.HotelOrder;
import com.ruoyi.system.domain.HotelOrderStatusLog;
import com.ruoyi.system.service.IHotelOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 酒店订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class HotelOrderServiceImpl implements IHotelOrderService 
{
    private static final Logger log = LoggerFactory.getLogger(HotelOrderServiceImpl.class);

    @Autowired
    private HotelOrderMapper hotelOrderMapper;

    @Autowired
    private HotelOrderStatusLogMapper hotelOrderStatusLogMapper;

    /**
     * 查询酒店订单
     * 
     * @param orderId 酒店订单主键
     * @return 酒店订单
     */
    @Override
    public HotelOrder selectHotelOrderByOrderId(Long orderId)
    {
        return hotelOrderMapper.selectHotelOrderByOrderId(orderId);
    }

    /**
     * 根据订单号查询酒店订单
     * 
     * @param orderNo 订单号
     * @return 酒店订单
     */
    @Override
    public HotelOrder selectHotelOrderByOrderNo(String orderNo)
    {
        return hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
    }

    /**
     * 根据微信交易号查询酒店订单
     * 
     * @param transactionId 微信交易号
     * @return 酒店订单
     */
    @Override
    public HotelOrder selectHotelOrderByTransactionId(String transactionId)
    {
        return hotelOrderMapper.selectHotelOrderByTransactionId(transactionId);
    }

    /**
     * 查询酒店订单列表
     * 
     * @param hotelOrder 酒店订单
     * @return 酒店订单
     */
    @Override
    public List<HotelOrder> selectHotelOrderList(HotelOrder hotelOrder)
    {
        return hotelOrderMapper.selectHotelOrderList(hotelOrder);
    }

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 酒店订单集合
     */
    @Override
    public List<HotelOrder> selectHotelOrderListByUserId(Long userId)
    {
        return hotelOrderMapper.selectHotelOrderListByUserId(userId);
    }

    /**
     * 根据openid查询订单列表
     * 
     * @param openid 微信openid
     * @return 酒店订单集合
     */
    @Override
    public List<HotelOrder> selectHotelOrderListByOpenid(String openid)
    {
        return hotelOrderMapper.selectHotelOrderListByOpenid(openid);
    }

    /**
     * 根据会议ID查询订单列表
     * 
     * @param conferenceId 会议ID
     * @return 酒店订单集合
     */
    @Override
    public List<HotelOrder> selectHotelOrderListByConferenceId(Long conferenceId)
    {
        return hotelOrderMapper.selectHotelOrderListByConferenceId(conferenceId);
    }

    /**
     * 查询待支付订单列表（超时订单）
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 酒店订单集合
     */
    @Override
    public List<HotelOrder> selectTimeoutPendingOrders(int timeoutMinutes)
    {
        return hotelOrderMapper.selectTimeoutPendingOrders(timeoutMinutes);
    }

    /**
     * 新增酒店订单
     * 
     * @param hotelOrder 酒店订单
     * @return 结果
     */
    @Override
    @Transactional
    public int insertHotelOrder(HotelOrder hotelOrder)
    {
        // 生成订单号
        if (StringUtils.isEmpty(hotelOrder.getOrderNo())) {
            hotelOrder.setOrderNo(generateOrderNo("HT"));
        }

        // 设置默认状态
        if (StringUtils.isEmpty(hotelOrder.getOrderStatus())) {
            hotelOrder.setOrderStatus(HotelOrder.OrderStatus.PENDING);
        }
        if (StringUtils.isEmpty(hotelOrder.getPaymentStatus())) {
            hotelOrder.setPaymentStatus(HotelOrder.PaymentStatus.UNPAID);
        }

        int result = hotelOrderMapper.insertHotelOrder(hotelOrder);
        
        // 记录状态变更日志
        if (result > 0) {
            logStatusChange(hotelOrder.getOrderId(), hotelOrder.getOrderNo(), null, 
                hotelOrder.getOrderStatus(), HotelOrderStatusLog.StatusType.ORDER, 
                "创建订单", hotelOrder.getCreateBy());
        }
        
        return result;
    }

    /**
     * 修改酒店订单
     * 
     * @param hotelOrder 酒店订单
     * @return 结果
     */
    @Override
    public int updateHotelOrder(HotelOrder hotelOrder)
    {
        return hotelOrderMapper.updateHotelOrder(hotelOrder);
    }

    /**
     * 更新订单支付信息
     * 
     * @param orderNo 订单号
     * @param transactionId 微信交易号
     * @param paymentTime 支付时间
     * @param orderStatus 订单状态
     * @param paymentStatus 支付状态
     * @param paymentMethod 支付方式
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrderPaymentInfo(String orderNo, String transactionId, Date paymentTime,
                                      String orderStatus, String paymentStatus, String paymentMethod)
    {
        // 查询原订单信息
        HotelOrder originalOrder = hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
        if (originalOrder == null) {
            log.error("订单不存在，订单号: {}", orderNo);
            return 0;
        }

        int result = hotelOrderMapper.updateOrderPaymentInfo(orderNo, transactionId, paymentTime, 
            orderStatus, paymentStatus, paymentMethod);
        
        // 记录状态变更日志
        if (result > 0) {
            if (!originalOrder.getOrderStatus().equals(orderStatus)) {
                logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getOrderStatus(), 
                    orderStatus, HotelOrderStatusLog.StatusType.ORDER, "支付成功", "system");
            }
            if (!originalOrder.getPaymentStatus().equals(paymentStatus)) {
                logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getPaymentStatus(), 
                    paymentStatus, HotelOrderStatusLog.StatusType.PAYMENT, "支付成功", "system");
            }
        }
        
        return result;
    }

    /**
     * 更新订单状态
     * 
     * @param orderNo 订单号
     * @param orderStatus 订单状态
     * @param updateBy 更新人
     * @param changeReason 变更原因
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrderStatus(String orderNo, String orderStatus, String updateBy, String changeReason)
    {
        // 查询原订单信息
        HotelOrder originalOrder = hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
        if (originalOrder == null) {
            log.error("订单不存在，订单号: {}", orderNo);
            return 0;
        }

        int result = hotelOrderMapper.updateOrderStatus(orderNo, orderStatus, updateBy);
        
        // 记录状态变更日志
        if (result > 0 && !originalOrder.getOrderStatus().equals(orderStatus)) {
            logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getOrderStatus(), 
                orderStatus, HotelOrderStatusLog.StatusType.ORDER, changeReason, updateBy);
        }
        
        return result;
    }

    /**
     * 更新支付状态
     * 
     * @param orderNo 订单号
     * @param paymentStatus 支付状态
     * @param updateBy 更新人
     * @param changeReason 变更原因
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePaymentStatus(String orderNo, String paymentStatus, String updateBy, String changeReason)
    {
        // 查询原订单信息
        HotelOrder originalOrder = hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
        if (originalOrder == null) {
            log.error("订单不存在，订单号: {}", orderNo);
            return 0;
        }

        int result = hotelOrderMapper.updatePaymentStatus(orderNo, paymentStatus, updateBy);
        
        // 记录状态变更日志
        if (result > 0 && !originalOrder.getPaymentStatus().equals(paymentStatus)) {
            logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getPaymentStatus(), 
                paymentStatus, HotelOrderStatusLog.StatusType.PAYMENT, changeReason, updateBy);
        }
        
        return result;
    }

    /**
     * 取消订单
     * 
     * @param orderNo 订单号
     * @param cancelReason 取消原因
     * @param updateBy 更新人
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelOrder(String orderNo, String cancelReason, String updateBy)
    {
        // 查询原订单信息
        HotelOrder originalOrder = hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
        if (originalOrder == null) {
            log.error("订单不存在，订单号: {}", orderNo);
            return 0;
        }

        int result = hotelOrderMapper.cancelOrder(orderNo, cancelReason, updateBy);
        
        // 记录状态变更日志
        if (result > 0) {
            logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getOrderStatus(), 
                HotelOrder.OrderStatus.CANCELLED, HotelOrderStatusLog.StatusType.ORDER, 
                cancelReason, updateBy);
        }
        
        return result;
    }

    /**
     * 确认订单
     * 
     * @param orderNo 订单号
     * @param updateBy 更新人
     * @return 结果
     */
    @Override
    @Transactional
    public int confirmOrder(String orderNo, String updateBy)
    {
        return updateOrderStatus(orderNo, HotelOrder.OrderStatus.CONFIRMED, updateBy, "确认订单");
    }

    /**
     * 批量删除酒店订单
     * 
     * @param orderIds 需要删除的酒店订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteHotelOrderByOrderIds(Long[] orderIds)
    {
        // 删除相关的状态变更记录
        for (Long orderId : orderIds) {
            hotelOrderStatusLogMapper.deleteHotelOrderStatusLogByOrderId(orderId);
        }
        return hotelOrderMapper.deleteHotelOrderByOrderIds(orderIds);
    }

    /**
     * 删除酒店订单信息
     * 
     * @param orderId 酒店订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteHotelOrderByOrderId(Long orderId)
    {
        // 删除相关的状态变更记录
        hotelOrderStatusLogMapper.deleteHotelOrderStatusLogByOrderId(orderId);
        return hotelOrderMapper.deleteHotelOrderByOrderId(orderId);
    }

    /**
     * 统计订单数量
     * 
     * @param hotelOrder 查询条件
     * @return 订单数量
     */
    @Override
    public int countHotelOrders(HotelOrder hotelOrder)
    {
        return hotelOrderMapper.countHotelOrders(hotelOrder);
    }

    /**
     * 统计用户订单数量
     * 
     * @param userId 用户ID
     * @param orderStatus 订单状态
     * @return 订单数量
     */
    @Override
    public int countUserOrders(Long userId, String orderStatus)
    {
        return hotelOrderMapper.countUserOrders(userId, orderStatus);
    }

    /**
     * 生成订单号
     * 
     * @param prefix 前缀
     * @return 订单号
     */
    @Override
    public String generateOrderNo(String prefix)
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        // 添加3位随机数
        int random = (int) (Math.random() * 1000);
        return prefix + timestamp + String.format("%03d", random);
    }

    /**
     * 处理超时订单
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 处理的订单数量
     */
    @Override
    @Transactional
    public int handleTimeoutOrders(int timeoutMinutes)
    {
        List<HotelOrder> timeoutOrders = hotelOrderMapper.selectTimeoutPendingOrders(timeoutMinutes);
        int count = 0;
        
        for (HotelOrder order : timeoutOrders) {
            int result = cancelOrder(order.getOrderNo(), "订单超时自动取消", "system");
            if (result > 0) {
                count++;
                log.info("订单超时自动取消，订单号: {}", order.getOrderNo());
            }
        }
        
        return count;
    }

    /**
     * 记录状态变更日志
     */
    private void logStatusChange(Long orderId, String orderNo, String oldStatus, String newStatus, 
                                String statusType, String changeReason, String operator)
    {
        try {
            hotelOrderStatusLogMapper.logStatusChange(orderId, orderNo, oldStatus, newStatus, 
                statusType, changeReason, operator);
        } catch (Exception e) {
            log.error("记录订单状态变更日志失败，订单号: {}", orderNo, e);
        }
    }
}
