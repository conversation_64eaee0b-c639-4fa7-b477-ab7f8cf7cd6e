package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.WechatPayParams;
import com.ruoyi.common.core.domain.WechatPayV3Result;
import com.ruoyi.common.core.domain.request.wechat.WechatPayRequest;

/**
 * 微信支付V3版本服务接口
 *
 * <AUTHOR>
 */
public interface IWechatPayService
{
    /**
     * 创建支付订单
     *
     * @param payRequest 支付请求参数
     * @return 小程序支付参数
     */
    WechatPayParams createPayOrder(WechatPayRequest payRequest);

    /**
     * 查询支付订单状态
     *
     * @param outTradeNo 商户订单号
     * @return 支付结果
     */
    WechatPayV3Result queryPayOrder(String outTradeNo);

    /**
     * 处理支付回调通知
     *
     * @param requestBody 微信回调的请求体
     * @param headers 请求头信息
     * @return 处理结果
     */
    String handlePayNotify(String requestBody, java.util.Map<String, String> headers);
}
