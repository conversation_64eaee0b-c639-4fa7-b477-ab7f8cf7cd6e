package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.HotelOrder;

/**
 * 酒店订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface IHotelOrderService 
{
    /**
     * 查询酒店订单
     * 
     * @param orderId 酒店订单主键
     * @return 酒店订单
     */
    public HotelOrder selectHotelOrderByOrderId(Long orderId);

    /**
     * 根据订单号查询酒店订单
     * 
     * @param orderNo 订单号
     * @return 酒店订单
     */
    public HotelOrder selectHotelOrderByOrderNo(String orderNo);

    /**
     * 根据微信交易号查询酒店订单
     * 
     * @param transactionId 微信交易号
     * @return 酒店订单
     */
    public HotelOrder selectHotelOrderByTransactionId(String transactionId);

    /**
     * 查询酒店订单列表
     * 
     * @param hotelOrder 酒店订单
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderList(HotelOrder hotelOrder);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderListByUserId(Long userId);

    /**
     * 根据openid查询订单列表
     * 
     * @param openid 微信openid
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderListByOpenid(String openid);

    /**
     * 根据会议ID查询订单列表
     * 
     * @param conferenceId 会议ID
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderListByConferenceId(Long conferenceId);

    /**
     * 查询待支付订单列表（超时订单）
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectTimeoutPendingOrders(int timeoutMinutes);

    /**
     * 新增酒店订单
     * 
     * @param hotelOrder 酒店订单
     * @return 结果
     */
    public int insertHotelOrder(HotelOrder hotelOrder);

    /**
     * 修改酒店订单
     * 
     * @param hotelOrder 酒店订单
     * @return 结果
     */
    public int updateHotelOrder(HotelOrder hotelOrder);

    /**
     * 更新订单支付信息
     * 
     * @param orderNo 订单号
     * @param transactionId 微信交易号
     * @param paymentTime 支付时间
     * @param orderStatus 订单状态
     * @param paymentStatus 支付状态
     * @param paymentMethod 支付方式
     * @return 结果
     */
    public int updateOrderPaymentInfo(String orderNo, String transactionId, java.util.Date paymentTime,
                                      String orderStatus, String paymentStatus, String paymentMethod);

    /**
     * 更新订单状态
     * 
     * @param orderNo 订单号
     * @param orderStatus 订单状态
     * @param updateBy 更新人
     * @param changeReason 变更原因
     * @return 结果
     */
    public int updateOrderStatus(String orderNo, String orderStatus, String updateBy, String changeReason);

    /**
     * 更新支付状态
     * 
     * @param orderNo 订单号
     * @param paymentStatus 支付状态
     * @param updateBy 更新人
     * @param changeReason 变更原因
     * @return 结果
     */
    public int updatePaymentStatus(String orderNo, String paymentStatus, String updateBy, String changeReason);

    /**
     * 取消订单
     * 
     * @param orderNo 订单号
     * @param cancelReason 取消原因
     * @param updateBy 更新人
     * @return 结果
     */
    public int cancelOrder(String orderNo, String cancelReason, String updateBy);

    /**
     * 确认订单
     * 
     * @param orderNo 订单号
     * @param updateBy 更新人
     * @return 结果
     */
    public int confirmOrder(String orderNo, String updateBy);

    /**
     * 批量删除酒店订单
     * 
     * @param orderIds 需要删除的酒店订单主键集合
     * @return 结果
     */
    public int deleteHotelOrderByOrderIds(Long[] orderIds);

    /**
     * 删除酒店订单信息
     * 
     * @param orderId 酒店订单主键
     * @return 结果
     */
    public int deleteHotelOrderByOrderId(Long orderId);

    /**
     * 统计订单数量
     * 
     * @param hotelOrder 查询条件
     * @return 订单数量
     */
    public int countHotelOrders(HotelOrder hotelOrder);

    /**
     * 统计用户订单数量
     * 
     * @param userId 用户ID
     * @param orderStatus 订单状态
     * @return 订单数量
     */
    public int countUserOrders(Long userId, String orderStatus);

    /**
     * 生成订单号
     * 
     * @param prefix 前缀
     * @return 订单号
     */
    public String generateOrderNo(String prefix);

    /**
     * 处理超时订单
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 处理的订单数量
     */
    public int handleTimeoutOrders(int timeoutMinutes);
}
