# 微信支付V3版本使用说明

## 概述

本文档介绍了微信支付V3版本的使用方法。相比V2版本，V3版本采用了更安全的RSA签名算法、JSON格式数据传输，并提供了更丰富的功能特性。

## V3版本主要改进

### 🔒 安全性提升
- **RSA签名**：使用RSA-SHA256签名算法，替代V2的MD5签名
- **证书验证**：支持微信平台证书验证，确保回调通知的真实性
- **HTTPS强制**：所有接口强制使用HTTPS协议

### 📊 数据格式优化
- **JSON格式**：使用JSON替代XML，数据结构更清晰
- **字段规范**：统一字段命名规范，提高可读性
- **类型安全**：明确字段类型，减少解析错误

### 🚀 功能增强
- **更丰富的订单信息**：支持商品详情、场景信息等
- **实时状态更新**：更准确的订单状态跟踪
- **更好的错误处理**：详细的错误码和错误描述

## 配置说明

### 1. 基础配置

在 `application.yml` 中配置V3版本参数：

```yaml
wechat:
  miniapp:
    appId: wx80ecd969d8dc4f6d
    appSecret: 780d9b814535e16b2b67fbd7f75fb9bf
  pay:
    # 商户号
    mchId: **********
    # V2版本API密钥（兼容性保留）
    apiSecret: 5e7fe213610c413f93be743e0c023749
    # V3版本APIv3密钥
    apiV3Key: your_api_v3_key_32_characters_long
    # 商户私钥文件路径
    privateKeyPath: /path/to/your/apiclient_key.pem
    # 商户证书序列号
    merchantSerialNumber: 1234567890ABCDEF1234567890ABCDEF12345678
    # 回调通知URL
    notifyUrl: https://your-domain.com/api/wechat/pay/notify
    # 支付超时时间（分钟）
    timeoutMinutes: 30
    # API版本选择：V2 或 V3
    apiVersion: V3
```

### 2. 证书配置

V3版本需要配置商户私钥文件：

1. **下载证书**：从微信商户平台下载API证书
2. **提取私钥**：从证书中提取私钥文件（apiclient_key.pem）
3. **配置路径**：将私钥文件路径配置到 `privateKeyPath`
4. **获取序列号**：从商户平台获取证书序列号

### 3. 安全注意事项

- 私钥文件权限设置为600（仅所有者可读写）
- APIv3密钥长度必须为32位
- 证书序列号区分大小写
- 定期更新证书和密钥

## API接口说明

### 1. 创建支付订单

**接口地址**：`POST /api/hotel/payment/create`

**请求示例**：
```json
{
    "outTradeNo": "HT202501170001",
    "body": "酒店预订-豪华大床房",
    "totalFee": 20000,
    "detail": "2024-03-16至2024-03-18，共2晚",
    "attach": "conferenceId:1,roomId:1",
    "spbillCreateIp": "*************"
}
```

**V3版本响应示例**：
```json
{
    "code": 200,
    "msg": "创建支付订单成功",
    "data": {
        "appId": "wx80ecd969d8dc4f6d",
        "timeStamp": "1705472400",
        "nonceStr": "abc123def456",
        "packageValue": "prepay_id=wx17154724001234567890",
        "signType": "RSA",
        "paySign": "Base64EncodedRSASignature"
    }
}
```

### 2. 查询支付订单

**接口地址**：`GET /api/hotel/payment/query?outTradeNo=HT202501170001`

**V3版本响应示例**：
```json
{
    "code": 200,
    "msg": "查询支付订单成功",
    "data": {
        "appid": "wx80ecd969d8dc4f6d",
        "mchid": "**********",
        "out_trade_no": "HT202501170001",
        "transaction_id": "4200001234567890123456789",
        "trade_type": "JSAPI",
        "trade_state": "SUCCESS",
        "trade_state_desc": "支付成功",
        "bank_type": "CMC",
        "attach": "conferenceId:1,roomId:1",
        "success_time": "2025-01-17T15:45:00+08:00",
        "amount": {
            "total": 20000,
            "payer_total": 20000,
            "currency": "CNY",
            "payer_currency": "CNY"
        },
        "payer": {
            "openid": "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o"
        }
    }
}
```

### 3. 支付回调通知

**V3版本回调格式**：
```json
{
    "id": "EV-2018022511223320873",
    "create_time": "2025-01-17T15:45:00+08:00",
    "resource_type": "encrypt-resource",
    "event_type": "TRANSACTION.SUCCESS",
    "summary": "支付成功",
    "resource": {
        "original_type": "transaction",
        "algorithm": "AEAD_AES_256_GCM",
        "ciphertext": "...",
        "associated_data": "transaction",
        "nonce": "..."
    }
}
```

## 核心类说明

### 1. WechatPayV3Utils - V3版本工具类

主要功能：
- `jsapiPay(WechatPayRequest)` - 小程序下单
- `generateMiniAppPayParams(String)` - 生成小程序支付参数
- `queryOrder(String)` - 查询订单状态
- `signWithRSA(String)` - RSA签名

### 2. WechatPayV3Request - V3版本请求实体

包含完整的V3版本请求参数结构：
- 基础订单信息
- 金额信息（Amount）
- 支付者信息（Payer）
- 商品详情（Detail）
- 场景信息（SceneInfo）

### 3. WechatPayV3Result - V3版本响应实体

包含完整的V3版本响应数据结构：
- 订单状态信息
- 支付结果详情
- 优惠信息
- 场景信息

## 小程序端集成

V3版本的小程序端调用方式与V2版本相同：

```javascript
// 创建支付订单
const paymentData = {
    outTradeNo: 'HT202501170001',
    body: '酒店预订-豪华大床房',
    totalFee: 20000, // 金额单位为分
    detail: '2024-03-16至2024-03-18，共2晚',
    attach: 'conferenceId:1,roomId:1'
};

PaymentUtils.createPayment(paymentData)
    .then(res => {
        console.log('支付成功', res);
        // 支付成功处理
    })
    .catch(err => {
        console.log('支付失败', err);
        // 支付失败处理
    });
```

## 版本切换

系统支持V2和V3版本的平滑切换：

### 1. 配置切换
```yaml
wechat:
  pay:
    apiVersion: V3  # 切换到V3版本
    # apiVersion: V2  # 切换到V2版本
```

### 2. 自动适配
- 控制器会根据配置自动选择对应的服务方法
- 回调处理会根据版本使用不同的数据格式
- 错误响应格式会自动适配

### 3. 兼容性保证
- V2版本的所有功能继续可用
- 可以在不停服的情况下进行版本切换
- 支持渐进式迁移

## 测试建议

### 1. 沙箱测试
- 使用微信支付沙箱环境进行测试
- 验证签名算法的正确性
- 测试各种支付场景

### 2. 证书验证
- 验证商户私钥配置正确
- 测试证书序列号匹配
- 确认回调签名验证通过

### 3. 性能测试
- 测试RSA签名的性能影响
- 验证并发支付请求处理能力
- 监控内存和CPU使用情况

## 常见问题

### 1. 签名验证失败
- 检查私钥文件格式和路径
- 确认证书序列号正确
- 验证签名字符串构建逻辑

### 2. 证书相关问题
- 确保证书未过期
- 检查证书权限设置
- 验证证书序列号格式

### 3. 回调处理问题
- 确认回调URL可访问
- 检查HTTPS证书有效性
- 验证回调数据解密逻辑

## 迁移指南

### 从V2迁移到V3

1. **配置更新**：添加V3版本相关配置
2. **证书准备**：下载并配置商户私钥
3. **测试验证**：在沙箱环境测试V3接口
4. **逐步切换**：先在测试环境切换，再在生产环境切换
5. **监控观察**：切换后密切监控支付成功率和异常情况

### 注意事项

- V3版本的回调通知格式完全不同
- 签名算法从MD5改为RSA-SHA256
- 错误处理机制有所调整
- 建议先在测试环境充分验证后再上线

## 总结

微信支付V3版本提供了更安全、更规范的支付接口。虽然迁移需要一定的工作量，但长远来看，V3版本的优势明显：

- **安全性更高**：RSA签名和证书验证
- **数据格式更清晰**：JSON格式易于处理
- **功能更丰富**：支持更多支付场景
- **维护性更好**：规范的接口设计

建议新项目直接使用V3版本，现有项目可以逐步迁移到V3版本。
