# 微信支付V3版本功能使用说明

## 概述

本文档介绍了微信支付V3版本下单功能的使用方法，主要实现了微信小程序支付的完整流程，包括统一下单、支付参数生成、订单查询和支付回调处理。

## 功能特性

- ✅ 微信支付V3版本统一下单
- ✅ 小程序支付参数生成（RSA签名）
- ✅ 支付订单状态查询
- ✅ 支付回调通知处理（JSON格式）
- ✅ RSA签名验证和安全保障
- ✅ 完善的错误处理和日志记录
- ✅ RESTful API接口

## 配置说明

### 1. 配置文件设置

在 `ruoyi-admin/src/main/resources/application.yml` 中的微信配置已包含支付相关配置：

```yaml
# 微信小程序配置
wechat:
  miniapp:
    # 小程序AppID
    appId: wx80ecd969d8dc4f6d
    # 小程序AppSecret
    appSecret: 780d9b814535e16b2b67fbd7f75fb9bf
    # access_token缓存时间（秒），默认7200秒（2小时）
    tokenCacheTime: 7200
  pay:
    # 商户号
    mchId: 1600000000
    # APIv3密钥
    apiV3Key: your_api_v3_key_32_characters_long
    # 商户私钥文件路径
    privateKeyPath: /path/to/your/apiclient_key.pem
    # 商户证书序列号
    merchantSerialNumber: your_merchant_serial_number_here
    # 支付回调通知URL
    notifyUrl: https://your-domain.com/api/wechat/pay/notify
    # 支付超时时间（分钟），默认30分钟
    timeoutMinutes: 30
```

**注意：** 请将配置中的值替换为实际的微信支付商户信息。

## 核心类说明

### 1. WechatPayConfig - 微信支付配置类

用于读取application.yml中的微信支付配置。

### 2. WechatPayUtils - 微信支付工具类

主要功能：
- `unifiedOrder(WechatPayRequest)` - 微信统一下单
- `generateMiniAppPayParams(String)` - 生成小程序支付参数
- `queryOrder(String)` - 查询订单状态

### 3. WechatPayService - 微信支付服务类

业务逻辑处理：
- `createPayOrder(WechatPayRequest)` - 创建支付订单
- `queryPayOrder(String)` - 查询支付订单状态
- `handlePayNotify(String)` - 处理支付回调通知
- `verifyPayNotify(String)` - 验证支付回调签名

### 4. 实体类

- `WechatPayRequest` - 支付请求参数
- `WechatPayResult` - 支付响应结果
- `WechatPayParams` - 小程序支付参数

## API接口说明

### 1. 创建支付订单

```http
POST /api/hotel/payment/create
Content-Type: application/json
Authorization: Bearer {token}

{
    "outTradeNo": "HT202501170001",
    "body": "酒店预订-豪华大床房",
    "totalFee": 20000,
    "detail": "2024-03-16至2024-03-18，共2晚",
    "attach": "conferenceId:1,roomId:1"
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "创建支付订单成功",
    "data": {
        "appId": "wx80ecd969d8dc4f6d",
        "timeStamp": "1705472400",
        "nonceStr": "abc123def456",
        "packageValue": "prepay_id=wx17154724001234567890",
        "signType": "MD5",
        "paySign": "A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6"
    }
}
```

### 2. 查询支付订单状态

```http
GET /api/hotel/payment/query?outTradeNo=HT202501170001
Authorization: Bearer {token}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "查询支付订单成功",
    "data": {
        "returnCode": "SUCCESS",
        "resultCode": "SUCCESS",
        "outTradeNo": "HT202501170001",
        "transactionId": "4200001234567890123456789",
        "tradeState": "SUCCESS",
        "tradeStateDesc": "支付成功",
        "totalFee": 20000,
        "timeEnd": "20250117154500"
    }
}
```

### 3. 支付回调通知（微信调用）

```http
POST /api/wechat/pay/notify
Content-Type: application/xml

<xml>
    <return_code><![CDATA[SUCCESS]]></return_code>
    <result_code><![CDATA[SUCCESS]]></result_code>
    <out_trade_no><![CDATA[HT202501170001]]></out_trade_no>
    <transaction_id><![CDATA[4200001234567890123456789]]></transaction_id>
    <total_fee>20000</total_fee>
    <time_end><![CDATA[20250117154500]]></time_end>
    <!-- 其他字段... -->
</xml>
```

## 小程序端集成

### 1. 调用支付接口

```javascript
// 创建支付订单
const paymentData = {
    outTradeNo: 'HT202501170001',
    body: '酒店预订-豪华大床房',
    totalFee: 20000, // 金额单位为分
    detail: '2024-03-16至2024-03-18，共2晚',
    attach: 'conferenceId:1,roomId:1'
};

wx.request({
    url: 'https://your-domain.com/api/hotel/payment/create',
    method: 'POST',
    header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token'),
        'Content-Type': 'application/json'
    },
    data: paymentData,
    success: (res) => {
        if (res.data.code === 200) {
            // 调起微信支付
            const payParams = res.data.data;
            wx.requestPayment({
                timeStamp: payParams.timeStamp,
                nonceStr: payParams.nonceStr,
                package: payParams.packageValue,
                signType: payParams.signType,
                paySign: payParams.paySign,
                success: (payRes) => {
                    console.log('支付成功', payRes);
                    // 支付成功处理
                    this.handlePaymentSuccess();
                },
                fail: (payErr) => {
                    console.log('支付失败', payErr);
                    // 支付失败处理
                    this.handlePaymentFail(payErr);
                }
            });
        } else {
            wx.showToast({
                title: res.data.msg,
                icon: 'none'
            });
        }
    }
});
```

### 2. 查询支付状态

```javascript
// 查询支付订单状态
wx.request({
    url: 'https://your-domain.com/api/hotel/payment/query',
    method: 'GET',
    header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token')
    },
    data: {
        outTradeNo: 'HT202501170001'
    },
    success: (res) => {
        if (res.data.code === 200) {
            const payResult = res.data.data;
            if (payResult.tradeState === 'SUCCESS') {
                console.log('支付成功');
                // 更新订单状态
            } else {
                console.log('支付状态：', payResult.tradeStateDesc);
            }
        }
    }
});
```

## 安全注意事项

### 1. 签名验证
- 所有微信支付接口都进行了签名验证
- 支付回调通知会验证微信的签名
- API密钥不会暴露给客户端

### 2. 参数校验
- 所有支付请求参数都进行了严格校验
- 金额必须为正整数（单位：分）
- 订单号必须唯一

### 3. 日志记录
- 记录所有支付相关操作的详细日志
- 包含用户信息、订单信息、支付结果等
- 便于问题排查和审计

## 错误处理

### 常见错误码

| 错误码 | 错误描述 | 解决方案 |
|--------|----------|----------|
| NOAUTH | 商户无此接口权限 | 检查商户号权限配置 |
| NOTENOUGH | 余额不足 | 提示用户余额不足 |
| ORDERPAID | 商户订单已支付 | 检查订单状态 |
| ORDERCLOSED | 订单已关闭 | 重新创建订单 |
| SYSTEMERROR | 系统错误 | 稍后重试 |

### 异常处理流程

1. **网络异常**：自动重试机制
2. **签名错误**：检查API密钥配置
3. **参数错误**：返回具体错误信息
4. **系统异常**：记录详细日志，返回友好提示

## 测试建议

### 1. 单元测试
- 测试签名生成算法
- 测试XML解析功能
- 测试参数校验逻辑

### 2. 集成测试
- 使用微信支付沙箱环境
- 测试完整支付流程
- 测试异常情况处理

### 3. 压力测试
- 测试高并发支付请求
- 测试回调通知处理能力
- 监控系统性能指标

## 部署注意事项

1. **HTTPS要求**：微信支付要求使用HTTPS协议
2. **回调URL**：确保回调URL可以被微信服务器访问
3. **防火墙配置**：开放相应端口
4. **证书配置**：如需退款功能，需要配置商户证书

## 监控和维护

建议监控以下指标：
- 支付成功率
- 支付响应时间
- 回调通知处理成功率
- 异常错误统计

定期检查：
- API密钥是否过期
- 证书是否需要更新
- 日志文件大小
- 系统性能指标
